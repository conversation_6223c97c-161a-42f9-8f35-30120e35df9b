import requests
import frappe
import json


class AdvantaService():
    def __init__(self):
        # Load configuration from site config with fallbacks
        self._api_url = frappe.conf.get("ADVANTA_API_URL", "https://quicksms.advantasms.com/api/services")
        self._partner_id = frappe.conf.get("ADVANTA_PARTNER_ID", "3804")
        self._short_code = frappe.conf.get("ADVANTA_SHORT_CODE", "GERTRUDES")
        self._token = frappe.conf.get("ADVANTA_TOKEN")

        # Load API key from site config
        self._api_key = frappe.conf.get("ADVANTA_API_KEY")

        if not self._api_key:
            print("ADVANTA_API_KEY not found in site config")

    def test_credentials(self):
        """Test if credentials are valid by checking account balance"""
        try:
            balance = self.check_balance()
            if balance <= 0:
                print("Advanta credentials validation failed or account has no balance")
        except Exception as e:
            print(f"Advanta credential test failed: {str(e)}")

    def send_sms(self, message, recipient) -> bool:
        """Send SMS via Advanta API"""
        try:
            if not self._api_key:
                frappe.log_error("ADVANTA_API_KEY is not configured", "Advanta SMS Error")
                return False

            request_url = f"{self._api_url}/sendsms/"
            shortcodes_to_try = [
                "GERTRUDES",
                "Gertrudes",
                self._short_code,
            ]

            for shortcode in shortcodes_to_try:
                request_payload = {
                    "apikey": self._api_key,
                    "partnerID": self._partner_id,
                    "message": message,
                    "shortcode": shortcode,
                    "mobile": recipient
                }

                headers = {'Content-Type': 'application/json'}
                result = requests.post(request_url, json=request_payload, headers=headers)

                if result.status_code == 200:
                    try:
                        json_result = result.json()
                        if 'responses' in json_result and len(json_result['responses']) > 0:
                            response_code = json_result['responses'][0].get('response-code')
                            response_description = json_result['responses'][0].get('response-description')
                            message_id = json_result['responses'][0].get('messageid')

                            if response_code == 200 and response_description == 'Success':
                                # Log successful SMS with message ID for tracking (shortened)
                                print(f"SMS sent successfully - Message ID: {message_id}")
                                return True
                            else:
                                # Log API errors (shortened)
                                print(f"Advanta API error: {response_code} - {response_description}")
                    except Exception:
                        # Skip logging to avoid character limit errors
                        continue

            # If all shortcodes failed
            print(f"All shortcodes failed for SMS to {recipient}")
            return False

        except Exception as e:
            print(f"Exception in AdvantaService.send_sms: {str(e)}")
            return False

    def check_balance(self) -> float:
        """Check account balance via Advanta API"""
        try:
            request_url = f"{self._api_url}/getbalance/"
            request_payload = {
                "apikey": self._api_key,
                "partnerID": self._partner_id,
            }

            headers = {'Content-Type': 'application/json'}
            result = requests.post(request_url, json=request_payload, headers=headers)

            if result.status_code == 200:
                json_result = result.json()
                if json_result.get("response-code") == 200:
                    return float(json_result.get("credit", 0))
            return 0

        except Exception:
            return 0
