{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-09-20 15:24:14.608508", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "appointment_phone", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 8, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient_name", "label": "Appointment Phone", "length": 0, "mandatory_depends_on": null, "modified": "2022-09-01 08:18:26.414098", "modified_by": "Administrator", "name": "Patient Appointment-appointment_phone", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2022-10-17 13:14:56.275253", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "branch", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 18, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "service_unit", "label": "Branch", "length": 0, "mandatory_depends_on": null, "modified": "2022-10-16 16:34:34.994189", "modified_by": "Administrator", "name": "Patient Appointment-branch", "no_copy": 0, "non_negative": 0, "options": "Branch", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 1, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-08-09 08:29:40.738515", "default": null, "depends_on": "eval:doc.not_in_system", "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "patient_names", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 6, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "not_in_system", "label": "Patient Name", "length": 0, "mandatory_depends_on": null, "modified": "2023-08-09 08:29:40.738515", "modified_by": "Administrator", "name": "Patient Appointment-patient_names", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-04 14:51:26.462111", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "priority_level", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 12, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_1", "label": "Priority Level", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-04 14:51:26.462111", "modified_by": "Administrator", "name": "Patient Appointment-priority_level", "no_copy": 0, "non_negative": 0, "options": "\nNormal\nPriority\nUrgent\nEmergency", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-04 14:53:42.596439", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vaccination", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 32, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "appointment_datetime", "label": "Vaccination", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-04 14:53:42.596439", "modified_by": "Administrator", "name": "Patient Appointment-vaccination", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-04 14:54:45.743272", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "visit_schedule", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 33, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "vaccination", "label": "Visit Schedule", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-04 14:54:45.743272", "modified_by": "Administrator", "name": "Patient Appointment-visit_schedule", "no_copy": 0, "non_negative": 0, "options": "Vaccine Administration", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-10-04 14:55:59.309787", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "vaccines", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 35, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "visit_schedule", "label": "Vaccines", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-04 14:56:50.104280", "modified_by": "Administrator", "name": "Patient Appointment-vaccines", "no_copy": 0, "non_negative": 0, "options": "Wellbaby Vaccine Details", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2023-08-09 08:25:06.561724", "default": null, "depends_on": "eval:!doc.patient", "description": null, "docstatus": 0, "dt": "Patient Appointment", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "not_in_system", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 4, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "patient", "label": "Not In System", "length": 0, "mandatory_depends_on": null, "modified": "2023-10-04 15:10:42.706211", "modified_by": "Administrator", "name": "Patient Appointment-not_in_system", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}], "custom_perms": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-13 13:51:17.319302", "delete": 0, "docstatus": 0, "email": 0, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2021-08-19 13:05:08.104419", "modified_by": "Administrator", "name": "0cc705d0d0", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": "permissions", "parenttype": "DocType", "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "GCH-Reception", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-13 13:51:17.341400", "delete": 0, "docstatus": 0, "email": 0, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2021-08-19 13:05:16.905464", "modified_by": "Administrator", "name": "c050aa05c9", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": "permissions", "parenttype": "DocType", "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "GCH-TriageNurse", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-14 08:29:54.269575", "delete": 0, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-06-14 08:29:54.269575", "modified_by": "Administrator", "name": "671cb7c4c2", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "System Manager", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-14 08:30:01.056894", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 1, "modified": "2022-06-14 08:30:01.056894", "modified_by": "Administrator", "name": "7e4c8535bc", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "System Manager", "select": 1, "set_user_permissions": 1, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-14 08:30:12.892146", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-06-14 08:30:12.892146", "modified_by": "Administrator", "name": "0cc157253a", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-14 08:30:12.962848", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-06-14 08:30:12.962848", "modified_by": "Administrator", "name": "1ac64b9193", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-06-14 08:30:13.024878", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-06-14 08:30:13.024878", "modified_by": "Administrator", "name": "d9ed08d296", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Physician", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-12-02 12:52:12.765340", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-07-28 12:50:37.965519", "modified_by": "Administrator", "name": "93a89bb6f4", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-12-02 12:52:13.563533", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-07-28 12:50:39.554068", "modified_by": "Administrator", "name": "750a131000", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:53:57.929812", "delete": 0, "docstatus": 0, "email": 0, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:53:57.929812", "modified_by": "Administrator", "name": "\"0cc705d0d0\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "GCH-Reception", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:53:57.990665", "delete": 1, "docstatus": 0, "email": 1, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:53:57.990665", "modified_by": "Administrator", "name": "\"c050aa05c9\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "GCH-TriageNurse", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:54:43.627592", "delete": 0, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:54:43.627592", "modified_by": "Administrator", "name": "\"671cb7c4c2\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "System Manager", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:54:49.426368", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 1, "modified": "2022-08-04 13:54:49.426368", "modified_by": "Administrator", "name": "\"7e4c8535bc\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "System Manager", "select": 1, "set_user_permissions": 1, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:55:00.318790", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:55:00.318790", "modified_by": "Administrator", "name": "\"0cc157253a\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:55:00.401042", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:55:00.401042", "modified_by": "Administrator", "name": "\"1ac64b9193\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:55:00.503500", "delete": 1, "docstatus": 0, "email": 1, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:55:00.503500", "modified_by": "Administrator", "name": "\"d9ed08d296\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "Physician", "select": 0, "set_user_permissions": 0, "share": 1, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:55:40.698370", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:55:40.698370", "modified_by": "Administrator", "name": "\"750a131000\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2022-08-04 13:55:43.167421", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-08-04 13:55:43.167421", "modified_by": "Administrator", "name": "\"93a89bb6f4\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2023-01-23 08:58:17.176524", "delete": 0, "docstatus": 0, "email": 0, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-12-06 07:29:29.509584", "modified_by": "Administrator", "name": "\"\"0cc705d0d0\"\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 0, "read": 1, "report": 0, "role": "GCH-Reception", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2023-01-23 08:58:17.201360", "delete": 1, "docstatus": 0, "email": 1, "export": 0, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-12-06 07:29:29.530161", "modified_by": "Administrator", "name": "\"\"c050aa05c9\"\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 1, "role": "GCH-TriageNurse", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2023-01-23 08:58:22.794474", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-12-06 07:29:35.721972", "modified_by": "Administrator", "name": "\"\"750a131000\"\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "amend": 0, "cancel": 0, "create": 1, "creation": "2023-01-23 08:58:23.264857", "delete": 1, "docstatus": 0, "email": 0, "export": 1, "idx": 0, "if_owner": 0, "import": 0, "modified": "2022-12-06 07:29:36.124041", "modified_by": "Administrator", "name": "\"\"93a89bb6f4\"\"", "owner": "Administrator", "parent": "Patient Appointment", "parentfield": null, "parenttype": null, "permlevel": 0, "print": 1, "read": 1, "report": 0, "role": "GCH-Doctor", "select": 1, "set_user_permissions": 0, "share": 0, "submit": 0, "write": 1}], "doctype": "Patient Appointment", "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-08-17 10:20:15.030020", "default_value": "0", "doc_type": "Patient Appointment", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "patient", "idx": 0, "modified": "2023-08-17 22:12:40.945144", "modified_by": "Administrator", "name": "Patient Appointment-patient-reqd", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-08-09 08:32:21.643105", "default_value": null, "doc_type": "Patient Appointment", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "patient", "idx": 0, "modified": "2023-08-09 08:32:21.643105", "modified_by": "Administrator", "name": "Patient Appointment-patient-depends_on", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "property": "depends_on", "property_type": "Data", "row_name": null, "value": "eval:!doc.not_in_system"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2023-08-09 08:26:51.934596", "default_value": null, "doc_type": "Patient Appointment", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "patient", "idx": 0, "modified": "2023-08-09 08:26:51.934596", "modified_by": "Administrator", "name": "Patient Appointment-patient-mandatory_depends_on", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "property": "mandatory_depends_on", "property_type": "Data", "row_name": null, "value": "eval:!doc.not_in_system"}, {"doc_type": "Patient Appointment", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment_for", "property": "fetch_from", "property_type": "Data", "value": ""}, {"doc_type": "Patient Appointment", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "appointment_for", "property": "default", "property_type": "Text", "value": "Practitioner"}], "sync_on_migrate": 1}