import frappe

def add_reschedule_sms_field():
    """Add reschedule SMS message field to Healthcare Settings"""

    try:
        # Check if the field already exists
        existing_field = frappe.db.exists("Custom Field", {
            "dt": "Healthcare Settings",
            "fieldname": "appointment_reschedule_msg"
        })

        if existing_field:
            print("✅ Reschedule message field already exists")
            return

        # Field for reschedule message template
        custom_field = frappe.get_doc({
            "doctype": "Custom Field",
            "dt": "Healthcare Settings",
            "fieldname": "appointment_reschedule_msg",
            "label": "Appointment Reschedule Message Template",
            "fieldtype": "Text",
            "description": "Template for appointment reschedule SMS. Use {{doc.field_name}} for dynamic values.",
            "default": "Hello {{doc.patient}}, Your appointment has been rescheduled to {{doc.appointment_date}} at {{doc.appointment_time}}. Thank you.",
            "insert_after": "appointment_confirmation_msg"
        })

        custom_field.insert(ignore_permissions=True)
        frappe.db.commit()

        print("✅ Added 'Appointment Reschedule Message Template' field")
        print("🎉 Reschedule SMS field added successfully!")
        print("\n📋 Next steps:")
        print("1. Go to Healthcare > Settings > Healthcare Settings")
        print("2. Customize the reschedule message template if needed")
        print("3. Test by rescheduling an appointment")

    except Exception as e:
        print(f"❌ Error adding reschedule field: {str(e)}")
        frappe.db.rollback()

if __name__ == "__main__":
    add_reschedule_sms_field()
