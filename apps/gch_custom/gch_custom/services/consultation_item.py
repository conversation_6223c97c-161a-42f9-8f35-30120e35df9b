import frappe
from frappe import _

def get_consulation_item(clinic: str, mode_of_payment: str = "") -> str:
        """
        Get consultation item from the clinc


        :param clinic: clinic to get the consultation item from
        :return: consultation item

        """
        try:
            clinic = frappe.get_doc("Healthcare Service Unit", clinic)

            if mode_of_payment:
                different_price = clinic.varying_prices
                if different_price:
                    if mode_of_payment == "Cash":
                        consultation_fee_item = clinic.cash_consultation_item
                        return consultation_fee_item
                    elif mode_of_payment == "Insurance":
                        consultation_fee_item = clinic.insurance_consultation_item
                        return consultation_fee_item
                    else:
                        consultation_fee_item = clinic.outpatient_consultation_item
                        return consultation_fee_item
                else:
                    consultation_fee_item = clinic.outpatient_consultation_item
                    return consultation_fee_item
            else:
                consultation_fee_item = clinic.outpatient_consultation_item
                return consultation_fee_item

        except Exception as e:
            print(e)
            frappe.log_error(e, "Error getting consultation item/ workflow_controller.py")
            return ""