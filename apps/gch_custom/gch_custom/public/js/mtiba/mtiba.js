// gch_custom/public/js/mtiba.js

frappe.ui.form.on("Sales Invoice", {
  refresh(frm) {
    frm.add_custom_button("Process MTIBA", async () => {
      const d = new frappe.ui.Dialog({
        title: "Process MTIBA Visit",
        fields: [
          { label: "Visit Code", fieldname: "visit_code", fieldtype: "Data", reqd: 1 },
        ],
        primary_action_label: "Submit",
        primary_action: async (values) => {
          d.hide();
          await frappe.call({
            method: "gch_insurance.mtiba.controller.process_mtiba",
            args: {
              visit_code: values.visit_code,
              invoice_name: frm.doc.name,
              encounter_name: frm.doc.encounter,
            },
            callback: (r) => {
              frappe.msgprint(r.message);
              frm.reload_doc();
            }
          });
        }
      });
      d.show();
    });
  }
});
