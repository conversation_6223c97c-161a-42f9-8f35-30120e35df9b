import json
import re

import frappe
from healthcare.healthcare.doctype.patient_appointment.patient_appointment import (
	PatientAppointment, invoice_appointment)
from frappe import _
from gch_messaging.utils.core import messaging
import datetime





def cleanNumber(x):
	num = str(x)
	cleanNum = num
	b = re.search("^254", num)
	if b:
		z = re.split("^254", num)
		cleanNum = z[1]

	a = re.search("^0", num)
	if a:
		x = re.split("^0", num)
		cleanNum = x[1]
	return cleanNum


class GCHPatientAppointment(PatientAppointment):
	def validate(self):
		# Set appointment_for if not set (workaround for missing allow_booking_for column)
		if not self.appointment_for:
			self.appointment_for = "Practitioner"

		# Set default appointment type if not provided
		self.set_default_appointment_type()
		self.validate_overlaps()
		# Custom validation that allows empty appointment_for
		self.validate_based_on_appointments_for_gch()
		self.validate_service_unit()
		self.set_appointment_datetime()
		self.validate_customer_created()
		self.set_status()
		self.set_title()

	def set_default_appointment_type(self):
		"""Set a default appointment type if not provided"""
		if not self.appointment_type:
			# Try to get the first available appointment type
			default_appointment_type = frappe.db.get_value("Appointment Type", {}, "name")

			if not default_appointment_type:
				# If no appointment types exist, create a default one
				default_appointment_type = self.create_default_appointment_type()

			if default_appointment_type:
				self.appointment_type = default_appointment_type

	def create_default_appointment_type(self):
		"""Create a default appointment type if none exists"""
		try:
			appointment_type = frappe.get_doc({
				"doctype": "Appointment Type",
				"appointment_type": "General Consultation",
				"default_duration": 30,
				"color": "#7575ff",
				"allow_booking_for": "Practitioner"
			})
			appointment_type.insert(ignore_permissions=True)
			return appointment_type.name
		except Exception as e:
			frappe.log_error(f"Error creating default appointment type: {str(e)}")
			return None

	def validate_based_on_appointments_for_gch(self):
		"""Custom validation that makes appointment_for optional and handles non-system patients"""

		# Skip patient validation if booking for someone not in the system
		if self.not_in_system:
			# Ensure patient_names is filled when not_in_system is checked
			if not self.patient_names:
				frappe.throw(_("Please enter Patient Name when booking for someone not in the system"))
			return

		# Ensure patient is selected when not booking for non-system patient
		if not self.patient:
			frappe.throw(_("Please select a Patient or check 'Not in System' to book for someone not registered"))

		if self.appointment_for:
			# fieldname: practitioner / department / service_unit
			appointment_for_field = frappe.scrub(self.appointment_for)

			# validate if respective field is set only if appointment_for is specified
			if not self.get(appointment_for_field):
				frappe.throw(
					_("Please enter {}").format(frappe.bold(self.appointment_for)),
					frappe.MandatoryError,
				)

			if self.appointment_for == "Practitioner":
				# appointments for practitioner are validated separately,
				# based on practitioner schedule
				return

			# Only validate duplicate appointments for registered patients
			if self.patient:
				# validate if patient already has an appointment for the day
				booked_appointment = frappe.db.exists(
					"Patient Appointment",
					{
						"patient": self.patient,
						"status": ["!=", "Cancelled"],
						appointment_for_field: self.get(appointment_for_field),
						"appointment_date": self.appointment_date,
						"name": ["!=", self.name],
					},
				)

				if booked_appointment:
					frappe.throw(
						_("Patient already has an appointment {} booked for {} on {}").format(
							frappe.get_desk_link("Patient Appointment", booked_appointment),
							frappe.bold(self.get(appointment_for_field)),
							frappe.bold(frappe.format(self.appointment_date, dict(fieldtype="Date"))),
						),
						frappe.DuplicateEntryError,
					)

			if not self.appointment_based_on_check_in:
				self.appointment_based_on_check_in = True

	def after_insert(self):
		self.update_prescription_details()
		self.set_payment_details()
		invoice_appointment(self)
		# self.update_fee_validity()
		send_confirmation_msg(self)

	def on_update(self):
		"""Called when appointment is updated - detect reschedules"""
		# Check if this is a reschedule by looking for date/time changes
		if not self.is_new() and (self.has_value_changed('appointment_date') or self.has_value_changed('appointment_time')):
			print(f"🔄 Appointment rescheduled: {self.name}")
			send_reschedule_msg(self)
		...

	# Overriding title method to include patients not saved on the system
	def set_title(self):
		self.title = _('{0} with {1}').format(self.patient_name or self.patient or self.patient_names,
			self.practitioner_name or self.practitioner)


def send_confirmation_msg(doc):
	"""Send appointment confirmation message if enabled in Healthcare Settings"""
	send_confirmation = frappe.db.get_single_value("Healthcare Settings", "send_appointment_confirmation")

	if send_confirmation:
		message = frappe.db.get_single_value("Healthcare Settings", "appointment_confirmation_msg")

		# Use default message if none configured
		if not message:
			message = "Hello {{doc.patient}}, Your appointment is on {{doc.appointment_date}} at {{doc.appointment_time}}. Thank you."

		try:
			result = send_message(doc, message)
			if result and len(result) == 2:
				sent, resp = result
				if sent:
					frappe.msgprint(_("Appointment booked successfully! SMS confirmation sent."), indicator="green")
				else:
					frappe.msgprint(_("Appointment booked successfully! SMS sending failed: {0}").format(resp), indicator="orange")
		except Exception as e:
			frappe.msgprint(_("Appointment booked successfully! SMS notification failed: {0}").format(str(e)), indicator="orange")

def send_reschedule_msg(doc):
	"""Send appointment reschedule message if enabled in Healthcare Settings"""

	send_confirmation = frappe.db.get_single_value("Healthcare Settings", "send_appointment_confirmation")

	if send_confirmation:
		# Use default reschedule message (no custom field needed)
		message = "Hello {{doc.patient}}, Your appointment has been rescheduled to {{doc.appointment_date}} at {{doc.appointment_time}}. Thank you."

		try:
			result = send_message(doc, message)
			if result and len(result) == 2:
				sent, resp = result
				if sent:
					frappe.msgprint(_("Appointment rescheduled successfully! SMS notification sent."), indicator="green")
				else:
					frappe.msgprint(_("Appointment rescheduled successfully! SMS sending failed: {0}").format(resp), indicator="orange")
			else:
				frappe.msgprint(_("Appointment rescheduled successfully!"), indicator="green")
		except Exception as e:
			frappe.msgprint(_("Appointment rescheduled successfully! SMS notification failed: {0}").format(str(e)), indicator="orange")

def send_message(doc, message):
	"""Send SMS message to patient using appointment or patient mobile number"""

	# Handle patients not in system
	if doc.not_in_system:
		patient_mobile = None
		patient_name = doc.patient_names or "Patient"
	else:
		patient_mobile = frappe.db.get_value("Patient", doc.patient, "mobile") if doc.patient else None
		patient_name = doc.patient

	branch_phones = None  # Branch phones functionality disabled

	# Create context with appropriate patient name
	context = {"doc": doc, "alert": doc, "comments": None, "phones": branch_phones}
	if doc.get("_comments"):
		context["comments"] = json.loads(doc.get("_comments"))

	# Override patient name for non-system patients in template rendering
	if doc.not_in_system and doc.patient_names:
		# Create a copy of doc with patient_names as patient for template rendering
		doc_copy = frappe._dict(doc.as_dict())
		doc_copy.patient = doc.patient_names
		context["doc"] = doc_copy

	# Render message template
	rendered_message = frappe.render_template(message, context)

	# Determine which phone number to use
	if patient_mobile:
		number = patient_mobile
	elif hasattr(doc, 'appointment_phone') and doc.appointment_phone:
		number = doc.appointment_phone
	else:
		patient_type = "non-system patient" if doc.not_in_system else "patient"
		frappe.msgprint(_("No phone number available for SMS notification to {0}. Please ensure the appointment phone is set.").format(patient_type), alert=True)
		return

	if not number:
		frappe.msgprint(_("Phone number is empty"), alert=True)
		return

	try:
		clean_number = cleanNumber(number)
		full_number = "254" + clean_number

		# Validate phone number format
		if len(clean_number) != 9:
			frappe.msgprint(_("Invalid phone number format: {0}").format(number), alert=True)
			return

		sent, resp = messaging.send_sms(
			recipient=full_number, message=rendered_message
		)

		# Return success status for caller to handle UI messages
		return sent, resp

	except Exception as e:
		frappe.msgprint(_("SMS not sent, error: {0}").format(str(e)), alert=True)
		return False, str(e)


def send_patient_appointment_reminder():
	print("Sending Appointment Reminders")
	if frappe.db.get_single_value("Healthcare Settings", "send_appointment_reminder"):
		remind_b4_timedelta = frappe.db.get_single_value("Healthcare Settings", "remind_before")
		remind_before_str = str(remind_b4_timedelta)
		time_components  = remind_before_str.split(".")
		time_components = time_components[0]
		remind_before = datetime.datetime.strptime(
			time_components,
			"%H:%M:%S",
		).time()
		reminder_dt = datetime.datetime.now() + datetime.timedelta(
			hours=remind_before.hour,
			minutes=remind_before.minute,
			seconds=remind_before.second,
		)

		appointment_list = frappe.db.get_all(
			"Patient Appointment",
			{
				"appointment_datetime": [
					"between",
					(datetime.datetime.now(), reminder_dt),
				],
				"reminded": 0,
				"status": ["!=", "Cancelled"],
			},
		)
		print(appointment_list)
		for appointment in appointment_list:
			doc = frappe.get_doc("Patient Appointment", appointment.name)
			message = frappe.db.get_single_value(
				"Healthcare Settings", "appointment_reminder_msg"
			)
			send_message(doc, message)
			frappe.db.set_value("Patient Appointment", doc.name, "reminded", 1)
			


def cancel_appointment(appointment_id):
	appointment = frappe.get_doc('Patient Appointment', appointment_id)
	if appointment.invoiced:
		sales_invoice = check_sales_invoice_exists(appointment)
		if sales_invoice and cancel_sales_invoice(sales_invoice):
			msg = _('Appointment {0} and Sales Invoice {1} cancelled').format(appointment.name, sales_invoice.name)
		else:
			msg = _('Appointment Cancelled. Please review and cancel the invoice {0}').format(sales_invoice.name)
	else:
		# fee_validity = manage_fee_validity(appointment)
		msg = _('Appointment Cancelled.')
		# if fee_validity:
		# 	msg += _('Fee Validity {0} updated.').format(fee_validity.name)

	frappe.msgprint(msg)
	

def cancel_sales_invoice(sales_invoice):
	if frappe.db.get_single_value('Healthcare Settings', 'automate_appointment_invoicing'):
		if len(sales_invoice.items) == 1:
			sales_invoice.cancel()
			return True
	return False
	

def check_sales_invoice_exists(appointment):
	sales_invoice = frappe.db.get_value('Sales Invoice Item', {
		'reference_dt': 'Patient Appointment',
		'reference_dn': appointment.name
	}, 'parent')

	if sales_invoice:
		sales_invoice = frappe.get_doc('Sales Invoice', sales_invoice)
		return sales_invoice
	return False



@frappe.whitelist()
def update_status_gch(appointment_id, status):
	frappe.db.set_value('Patient Appointment', appointment_id, 'status', status)
	appointment_booked = True
	if status == 'Cancelled':
		appointment_booked = False
		cancel_appointment(appointment_id)
