from erpnext.stock.doctype.stock_entry.stock_entry import StockEntry
import frappe
from frappe import _
from erpnext.stock.utils import get_stock_balance
from erpnext.stock.doctype.batch.batch import get_batch_qty
from frappe.utils import flt, get_datetime
from datetime import timedelta

class GCHStockEntry(StockEntry):
    def update_stock_ledger(self):
        """Override to bypass Serial and Batch Bundle for Material Receipts and ensure correct qty_after_transaction"""
        sl_entries = []
        for d in self.get("items"):
            if not frappe.get_cached_value("Item", d.item_code, "is_stock_item"):
                continue

            # Get target warehouse for Material Receipt
            warehouse = d.t_warehouse if self.purpose == "Material Receipt" else d.s_warehouse

            # Handle batch-enabled items
            if frappe.get_cached_value("Item", d.item_code, "has_batch_no"):
                # Force batch_no = item_code
                batch_no = d.item_code
                if not frappe.db.exists("Batch", {"batch_id": d.item_code, "item": d.item_code}):
                    # Create new batch
                    batch_doc = frappe.get_doc({
                        "doctype": "Batch",
                        "item": d.item_code,
                        "batch_id": d.item_code,
                        "creation": get_datetime(),
                        "expiry_date": get_datetime().date() + timedelta(days=365 * 50),
                        "company": self.company
                    }).insert(ignore_permissions=True)
                    batch_no = batch_doc.name
                    # Zero out and disable other batches
                    batches = frappe.db.sql("""
                        SELECT name AS batch_no
                        FROM `tabBatch`
                        WHERE item = %s AND batch_id != %s AND disabled = 0
                    """, (d.item_code, d.item_code), as_dict=True)
                    for batch in batches:
                        frappe.db.set_value("Batch", batch.batch_no, "batch_qty", 0)
                        frappe.db.set_value("Batch", batch.batch_no, "disabled", 1)
                        frappe.log_error(
                            f"Disabled batch: item={d.item_code}, batch_no={batch.batch_no}",
                            "Stock Entry Batch Debug"
                        )
                else:
                    batch_no = frappe.get_value("Batch", {"batch_id": d.item_code, "item": d.item_code}, "name")
                    frappe.db.set_value("Batch", batch_no, "disabled", 0)

                d.batch_no = batch_no
                # Bypass Serial and Batch Bundle for Material Receipts
                if self.purpose == "Material Receipt":
                    d.serial_and_batch_bundle = None
                    frappe.log_error(
                        f"Bypassed bundle: item={d.item_code}, set serial_and_batch_bundle=None",
                        "Stock Entry Bundle Debug"
                    )

                # Get previous batch quantity
                previous_batch_qty = flt(get_batch_qty(batch_no, warehouse, self.posting_date, self.posting_time), d.precision("qty"))
                frappe.log_error(
                    f"Previous batch qty: item={d.item_code}, batch_no={batch_no}, warehouse={warehouse}, qty={previous_batch_qty}",
                    "Stock Entry Batch Debug"
                )

                # Use item qty directly
                actual_qty = flt(d.qty, d.precision("qty")) if self.purpose == "Material Receipt" else -flt(d.qty, d.precision("qty"))

                # Update batch quantity
                new_batch_qty = previous_batch_qty + actual_qty
                frappe.db.set_value("Batch", batch_no, "batch_qty", new_batch_qty)
                frappe.log_error(
                    f"Updated batch: item={d.item_code}, batch_no={batch_no}, old_qty={previous_batch_qty}, new_qty={new_batch_qty}",
                    "Stock Entry Batch Debug"
                )
            else:
                # Non-batch items
                previous_batch_qty = flt(get_stock_balance(d.item_code, warehouse, self.posting_date, self.posting_time), d.precision("qty"))
                batch_no = None
                actual_qty = flt(d.qty, d.precision("qty")) if self.purpose == "Material Receipt" else -flt(d.qty, d.precision("qty"))
                frappe.log_error(
                    f"Previous stock qty: item={d.item_code}, warehouse={warehouse}, qty={previous_batch_qty}",
                    "Stock Entry Debug"
                )

            # Create SLE
            sl_entry = self.get_sl_entries(d, {
                "actual_qty": actual_qty,
                "warehouse": warehouse,
                "batch_no": batch_no,
                "valuation_rate": flt(d.valuation_rate or 0, d.precision("valuation_rate")),
                "qty_after_transaction": previous_batch_qty + actual_qty,
                "serial_and_batch_bundle": None  # Force no bundle for Material Receipts
            })
            sl_entries.append(sl_entry)

            # Log SLE details
            frappe.log_error(
                f"SLE: item={sl_entry.item_code}, warehouse={sl_entry.warehouse}, actual_qty={sl_entry.actual_qty}, qty_after_transaction={sl_entry.qty_after_transaction}, batch_no={sl_entry.batch_no}, bundle={sl_entry.serial_and_batch_bundle}",
                "Stock Entry SLE Debug"
            )

            # Update Bin
            bin_name = frappe.db.get_value("Bin", {"item_code": d.item_code, "warehouse": warehouse}, "name")
            if not bin_name:
                frappe.get_doc({
                    "doctype": "Bin",
                    "item_code": d.item_code,
                    "warehouse": warehouse,
                    "actual_qty": 0,
                    "projected_qty": 0,
                    "stock_value": 0
                }).insert(ignore_permissions=True)
            bin_doc = frappe.get_doc("Bin", {"item_code": d.item_code, "warehouse": warehouse})
            bin_doc.actual_qty = previous_batch_qty + actual_qty
            bin_doc.projected_qty = bin_doc.actual_qty
            bin_doc.stock_value = bin_doc.actual_qty * flt(d.valuation_rate or 0, d.precision("valuation_rate"))
            bin_doc.save(ignore_permissions=True)
            frappe.log_error(
                f"Updated Bin: item={d.item_code}, warehouse={warehouse}, actual_qty={bin_doc.actual_qty}, stock_value={bin_doc.stock_value}",
                "Stock Entry Bin Debug"
            )

        # Commit SLEs and Bin updates
        self.make_sl_entries(sl_entries, allow_negative_stock=frappe.db.get_single_value("Stock Settings", "allow_negative_stock"))
        frappe.db.commit()

    def get_sl_entries(self, d, args):
        """Override to ensure correct SLE fields without Serial and Batch Bundle for Material Receipts"""
        sl_dict = frappe._dict({
            "doctype": "Stock Ledger Entry",
            "item_code": d.item_code,
            "warehouse": args.get("warehouse"),
            "posting_date": self.posting_date,
            "posting_time": self.posting_time,
            "voucher_type": self.doctype,
            "voucher_no": self.name,
            "voucher_detail_no": d.name,
            "company": self.company,
            "stock_uom": frappe.db.get_value("Item", d.item_code, "stock_uom"),
            "actual_qty": args.get("actual_qty", 0),
            "qty_after_transaction": args.get("qty_after_transaction", 0),
            "batch_no": args.get("batch_no"),
            "valuation_rate": flt(args.get("valuation_rate", 0), d.precision("valuation_rate")),
            "stock_value": flt(args.get("qty_after_transaction", 0)) * flt(args.get("valuation_rate", 0), d.precision("valuation_rate")),
            "stock_value_difference": flt(args.get("actual_qty", 0)) * flt(args.get("valuation_rate", 0), d.precision("valuation_rate")),
            "serial_and_batch_bundle": args.get("serial_and_batch_bundle"),
            "is_cancelled": 0
        })
        return sl_dict