2025-06-23 20:41:08,038 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_fa1b19b2f922012e'@'172.18.0.1' (using password: YES)")
2025-06-23 20:42:08,155 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_fa1b19b2f922012e'@'172.18.0.1' (using password: YES)")
2025-06-23 20:43:08,270 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_fa1b19b2f922012e'@'172.18.0.1' (using password: YES)")
2025-06-23 20:44:08,387 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_fa1b19b2f922012e'@'172.18.0.1' (using password: YES)")
2025-06-24 09:44:58,510 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
  File "/usr/lib/python3.10/socket.py", line 857, in create_connection
    raise err
  File "/usr/lib/python3.10/socket.py", line 845, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-06-25 07:32:14,810 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for gch.local
2025-06-25 07:32:14,878 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for gch.local
2025-06-25 07:32:14,922 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for gch.local
2025-06-25 07:32:15,009 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for gch.local
2025-06-25 07:32:15,063 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for gch.local
2025-06-25 07:32:15,195 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for gch.local
2025-06-25 07:32:15,481 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for gch.local
2025-06-25 07:32:15,568 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for gch.local
2025-06-25 07:32:15,578 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for gch.local
2025-06-25 07:32:15,624 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for gch.local
2025-06-25 07:32:15,639 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for gch.local
2025-06-25 07:32:15,655 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for gch.local
2025-06-25 07:32:15,731 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for gch.local
2025-06-26 08:02:54,013 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for gch.local
2025-06-26 17:12:02,079 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-06-26 17:13:02,623 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-06-26 17:14:03,177 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-06-26 17:15:03,406 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-06-26 17:16:04,194 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-06-26 17:17:04,828 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for gch.local
2025-07-14 12:31:42,703 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:32:42,845 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:34:34,766 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:35:34,903 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:36:35,021 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:37:35,146 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:38:35,261 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:39:35,389 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:40:35,554 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:41:35,678 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:42:35,792 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:43:35,913 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:44:36,029 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:45:36,147 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:46:36,266 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:47:36,386 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:48:36,502 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:49:36,608 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:50:36,716 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:51:36,863 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:52:37,002 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-14 12:53:37,028 ERROR scheduler Exception in Enqueue Events for Site gch.local
Traceback (most recent call last):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/job/gch_v2/gch-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/job/gch_v2/gch-bench/env/lib/python3.10/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_17cb8a0ae1173f21'@'**************'")
2025-07-15 18:34:18,582 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for gch.local
2025-07-15 18:34:19,039 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for gch.local
2025-07-16 06:43:39,480 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for gch.local
2025-07-16 06:43:39,546 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for gch.local
2025-07-16 06:43:39,597 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for gch.local
2025-07-16 06:43:39,611 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for gch.local
2025-07-16 06:43:39,728 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for gch.local
2025-07-16 06:43:39,733 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for gch.local
2025-07-16 06:43:39,754 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for gch.local
2025-07-16 12:15:59,744 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
2025-07-16 12:17:00,301 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
2025-07-16 12:18:00,707 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
2025-07-16 12:19:01,075 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
2025-07-16 12:20:01,620 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
2025-07-16 12:21:02,436 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for gch.local
