2025-06-23 08:39:49,003 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'app_path': 'stock'}
2025-06-23 08:45:56,712 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'app_path': 'buying'}
2025-06-23 08:46:19,022 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {}
2025-06-23 09:58:49,538 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient', 'name': 'new-patient-fpoxyrdehw', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-06-23 09:58:56,005 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient', 'name': 'new-patient-fpoxyrdehw', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-06-24 09:47:00,417 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'frappe.core.doctype.user.user.reset_password', 'user': '<EMAIL>'}
2025-06-24 10:45:32,611 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'app_path': 'home'}
2025-06-24 10:47:23,797 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'app_path': 'home'}
2025-06-24 11:49:55,463 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'All Healthcare Service Units - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-24 11:50:15,283 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'All Healthcare Service Units - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-24 12:36:54,476 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'gender': '', 'cmd': 'gch_custom.services.get_possible_patient_matches'}
2025-06-24 14:00:09,117 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'All Healthcare Service Units - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-24 14:00:21,241 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'All Healthcare Service Units - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 09:43:53,041 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Triage - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 09:44:57,674 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Triage - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 15:15:53,573 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Pharmacy - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 15:16:27,733 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Pharmacy - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 15:16:52,751 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Triage - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 15:17:48,341 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Pharmacy - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-06-25 15:35:34,867 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'docs': '{"name":"Customize Form","istable":0,"is_calendar_and_gantt":0,"editable_grid":0,"quick_entry":1,"track_changes":0,"track_views":0,"allow_auto_repeat":0,"allow_import":1,"queue_in_background":0,"max_attachments":0,"allow_copy":0,"make_attachments_public":0,"show_title_field_in_link":0,"translated_doctype":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"email_append_to":0,"sort_order":"DESC","doctype":"Customize Form","fields":[{"docstatus":0,"doctype":"Customize Form Field","name":"mju15fcsk0","is_system_generated":0,"fieldtype":"Data","non_negative":0,"reqd":1,"unique":1,"is_virtual":0,"in_list_view":1,"in_standard_filter":0,"in_global_search":0,"in_preview":0,"bold":0,"no_copy":0,"allow_in_quick_entry":0,"translatable":0,"sort_options":0,"fetch_if_empty":0,"show_dashboard":0,"permlevel":0,"hidden":0,"read_only":0,"collapsible":0,"allow_bulk_edit":0,"ignore_user_permissions":0,"allow_on_submit":0,"report_hide":0,"remember_last_selected_value":0,"hide_border":0,"ignore_xss_filter":0,"in_filter":0,"hide_seconds":0,"hide_days":0,"print_hide":0,"print_hide_if_no_value":0,"is_custom_field":0,"parent":"Customize Form","parentfield":"fields","parenttype":"Customize Form","idx":1,"label":"Branch","fieldname":"branch","length":0,"columns":0},{"docstatus":0,"doctype":"Customize Form Field","name":"Branch-custom_short_name","is_system_generated":0,"fieldtype":"Data","non_negative":0,"reqd":0,"unique":0,"is_virtual":0,"in_list_view":0,"in_standard_filter":0,"in_global_search":0,"in_preview":0,"bold":0,"no_copy":0,"allow_in_quick_entry":0,"translatable":1,"precision":"","sort_options":0,"fetch_if_empty":0,"show_dashboard":0,"permlevel":0,"hidden":0,"read_only":0,"collapsible":0,"allow_bulk_edit":0,"ignore_user_permissions":0,"allow_on_submit":0,"report_hide":0,"remember_last_selected_value":0,"hide_border":0,"ignore_xss_filter":0,"in_filter":0,"hide_seconds":0,"hide_days":0,"print_hide":0,"print_hide_if_no_value":0,"is_custom_field":1,"parent":"Customize Form","parentfield":"fields","parenttype":"Customize Form","idx":2,"fieldname":"custom_short_name","label":"Short Name","length":0,"columns":0}],"states":[],"links":[],"actions":[],"doc_type":"Branch","label":"","autoname":"field:branch","sort_field":"modified","__unsaved":0}', 'method': 'reset_to_defaults', 'cmd': 'run_doc_method'}
2025-06-26 12:08:34,466 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"name":"John Doe - *********","owner":"<EMAIL>","creation":"2025-06-25 07:57:26.987566","modified":"2025-06-26 11:52:19.731331","modified_by":"Administrator","docstatus":0,"idx":0,"naming_series":"HLC-PAT-.YYYY.-","first_name":"Allyson","middle_name":"","last_name":"Felix","uhid_code":"*********","sex":"Male","blood_group":"","dob":"2024-06-25","gch_patient_age":"1 Year(s) 0 Month(s) 2 Day(s)","patient_name":"Mary Johnson","uhid":"<svg height=\\"88px\\" width=\\"100%\\" x=\\"0px\\" y=\\"0px\\" viewBox=\\"0 0 323 88\\" xmlns=\\"http://www.w3.org/2000/svg\\" version=\\"1.1\\" style=\\"transform: translate(0,0)\\" data-barcode-value=\\"*********\\"><rect x=\\"0\\" y=\\"0\\" width=\\"323\\" height=\\"88\\" style=\\"fill:#ffffff;\\"></rect><g transform=\\"translate(10, 10)\\" style=\\"fill:#000000;\\"><rect x=\\"0\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"9\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"18\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"33\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"45\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"54\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"66\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"72\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"84\\" y=\\"0\\" width=\\"12\\" height=\\"50\\"></rect><rect x=\\"99\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"108\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"117\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"132\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"141\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"159\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"165\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"174\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"186\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"198\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"210\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"219\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"231\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"237\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"243\\" y=\\"0\\" width=\\"12\\" height=\\"50\\"></rect><rect x=\\"264\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"279\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"291\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"297\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><text style=\\"font: 16px monospace\\" text-anchor=\\"middle\\" x=\\"151.5\\" y=\\"68\\">*********</text></g></svg>","status":"Active","inpatient_status":"","report_preference":"","is_vip_patient":0,"is_staff":0,"is_adult":0,"is_a_crystal_or_vip_patient":0,"sent_to_pacs":0,"marital_status":"","requires_support_to_fill_out_form":0,"requires_translation":0,"requires_support_to_fill_out_form_options":"","requires_translation_options":"","uses_assistive_devices_in_movement":0,"uses_assistive_devices_in_movement_options":"","requires_support_to_move_around":0,"requires_support_to_move_around_options":"","consent_to_receive_communication":0,"more_info_needed_to_improve_communication":0,"requires_assistive_communication_devices":0,"requires_additional_privacy":0,"cultural_or_religious_preferences_options":"","requires_assistive_communication_devices_options":"","consent_to_receive_communication_options":"","cultural_or_religious_preferences":0,"customer":"Mary Johnson","customer_group":"All Customer Groups","territory":"All Territories","default_currency":"KES","default_price_list":"Standard Selling","language":"en","invite_user":0,"has_no_drug_allergy":0,"has_no_food_allergy":0,"doctype":"Patient","_employer_details":[],"patient_medical_cover_details":[],"parents":[],"drug_allergies":[],"patient_relation":[],"drug_allergy":[],"food_allergy":[],"food_allergies":[],"other_allergies":[],"__onload":{"addr_list":[],"contact_list":[],"dashboard_info":[]},"__last_sync_on":"2025-06-26T09:08:04.614Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-26 12:08:47,874 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"name":"John Doe - *********","owner":"<EMAIL>","creation":"2025-06-25 07:57:26.987566","modified":"2025-06-26 11:52:19.731331","modified_by":"Administrator","docstatus":0,"idx":0,"naming_series":"HLC-PAT-.YYYY.-","first_name":"Allyson","middle_name":"","last_name":"Felix","uhid_code":"*********","sex":"Male","blood_group":"","dob":"2024-06-25","gch_patient_age":"1 Year(s) 0 Month(s) 2 Day(s)","patient_name":"Mary Johnson","uhid":"<svg height=\\"88px\\" width=\\"100%\\" x=\\"0px\\" y=\\"0px\\" viewBox=\\"0 0 323 88\\" xmlns=\\"http://www.w3.org/2000/svg\\" version=\\"1.1\\" style=\\"transform: translate(0,0)\\" data-barcode-value=\\"*********\\"><rect x=\\"0\\" y=\\"0\\" width=\\"323\\" height=\\"88\\" style=\\"fill:#ffffff;\\"></rect><g transform=\\"translate(10, 10)\\" style=\\"fill:#000000;\\"><rect x=\\"0\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"9\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"18\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"33\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"45\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"54\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"66\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"72\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"84\\" y=\\"0\\" width=\\"12\\" height=\\"50\\"></rect><rect x=\\"99\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"108\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"117\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"132\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"141\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"159\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"165\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"174\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"186\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"198\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"210\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"219\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"231\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"237\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"243\\" y=\\"0\\" width=\\"12\\" height=\\"50\\"></rect><rect x=\\"264\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><rect x=\\"279\\" y=\\"0\\" width=\\"9\\" height=\\"50\\"></rect><rect x=\\"291\\" y=\\"0\\" width=\\"3\\" height=\\"50\\"></rect><rect x=\\"297\\" y=\\"0\\" width=\\"6\\" height=\\"50\\"></rect><text style=\\"font: 16px monospace\\" text-anchor=\\"middle\\" x=\\"151.5\\" y=\\"68\\">*********</text></g></svg>","status":"Active","inpatient_status":"","report_preference":"","is_vip_patient":0,"is_staff":0,"is_adult":0,"is_a_crystal_or_vip_patient":0,"sent_to_pacs":0,"marital_status":"","requires_support_to_fill_out_form":0,"requires_translation":0,"requires_support_to_fill_out_form_options":"","requires_translation_options":"","uses_assistive_devices_in_movement":0,"uses_assistive_devices_in_movement_options":"","requires_support_to_move_around":0,"requires_support_to_move_around_options":"","consent_to_receive_communication":0,"more_info_needed_to_improve_communication":0,"requires_assistive_communication_devices":0,"requires_additional_privacy":0,"cultural_or_religious_preferences_options":"","requires_assistive_communication_devices_options":"","consent_to_receive_communication_options":"","cultural_or_religious_preferences":0,"customer":"Mary Johnson","customer_group":"All Customer Groups","territory":"All Territories","default_currency":"KES","default_price_list":"Standard Selling","language":"en","invite_user":0,"has_no_drug_allergy":0,"has_no_food_allergy":0,"doctype":"Patient","_employer_details":[],"patient_medical_cover_details":[],"parents":[],"drug_allergies":[],"patient_relation":[],"drug_allergy":[],"food_allergy":[],"food_allergies":[],"other_allergies":[],"__onload":{"addr_list":[],"contact_list":[],"dashboard_info":[]},"__last_sync_on":"2025-06-26T09:08:04.614Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-26 12:08:58,376 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient', 'name': 'John Doe - *********', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-06-26 16:50:13,189 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient', 'name': 'John Doe - *********', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-06-26 16:50:23,986 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient', 'name': 'John Doe - *********', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-07-08 14:33:04,910 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:33:21,722 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:33:32,680 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:33:48,982 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:34:00,053 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:34:20,946 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:47:59,008 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:48:12,795 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:48:28,327 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:50:46,123 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 14:50:54,451 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'cmd': 'gch_custom.services.rest.get_last_pe_detail'}
2025-07-08 16:09:23,307 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:24,539 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:25,613 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:26,757 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:28,714 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:30,425 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:32,258 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:34,488 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:36,748 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:39,531 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:41,905 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:43,206 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:45,025 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:47,081 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:48,819 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:50,793 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:53,119 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:55,841 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:09:58,546 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:10:01,119 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:10:03,430 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:10:06,054 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:10:08,205 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:16,845 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:18,020 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:19,096 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:20,160 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:21,230 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:22,310 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:23,396 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:24,410 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:25,637 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:26,570 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:27,104 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:31,183 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:32,193 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:12:33,253 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:45,141 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:46,272 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:47,247 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:48,264 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:49,311 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:50,380 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:51,488 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:53,094 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:54,106 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:55,068 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:56,046 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:57,111 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:58,130 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:15:59,156 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:00,177 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:01,200 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:02,671 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:03,751 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:04,762 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:05,964 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:07,222 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:08,394 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:09,600 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:10,687 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:12,069 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:13,995 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:15,244 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:16,438 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:17,698 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:19,572 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-08 16:16:21,091 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doctype': 'Patient Encounter', 'filters': 'John Doe - *********5', 'cmd': 'gch_custom.services.rest.fetch_data_from_recent_encounter2'}
2025-07-10 15:15:04,369 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Panagani - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-07-11 08:42:18,513 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'Panagani - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-07-14 05:42:50,569 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'service_unit': 'All Healthcare Service Units - GCH', 'cmd': 'gch_queue.services.set_station'}
2025-07-14 12:04:38,409 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"docstatus":0,"doctype":"Appointment","name":"new-appointment-iobiwtsnnn","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","__run_link_triggers":1,"scheduled_time":"2025-07-02 12:04:15","customer_name":"YK9","customer_email":"<EMAIL>"}', 'cmd': 'frappe.client.save'}
2025-07-14 12:04:50,603 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"docstatus":0,"doctype":"Appointment","name":"new-appointment-iobiwtsnnn","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","__run_link_triggers":1,"scheduled_time":"2025-07-02 12:04:15","customer_name":"YK9","customer_email":"<EMAIL>"}', 'cmd': 'frappe.client.save'}
2025-07-14 12:22:09,528 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"docstatus":0,"doctype":"Appointment","name":"new-appointment-ilyfkaowqg","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","__run_link_triggers":1,"scheduled_time":"2025-07-11 12:21:48","customer_name":"Kevin Wanoyonyi","customer_email":"<EMAIL>"}', 'cmd': 'frappe.client.save'}
2025-07-17 06:49:40,863 ERROR frappe New Exception collected in error log
Site: gch.local
Form Dict: {'doc': '{"name":"HLC-APP-2025-00003","owner":"Administrator","creation":"2025-07-16 13:41:34.209873","modified":"2025-07-17 06:24:57.377423","modified_by":"Administrator","docstatus":0,"idx":0,"naming_series":"HLC-APP-.YYYY.-","title":"Mama Wangila Wanjira with Dan Duncan","status":"Open","appointment_type":"General Consultation","appointment_for":"Practitioner","priority_level":"Urgent","company":"Gertrude\'s Children\'s Hospital","practitioner":"HLC-PRAC-2025-00004","practitioner_name":"Dan Duncan","department":"Microbiology","service_unit":"Panagani - GCH","branch":"Muthaiga","appointment_date":"2025-07-17","patient":"John Doe - *********5","not_in_system":0,"patient_name":"Mama Wangila Wanjira","appointment_phone":"*********","patient_sex":"Male","patient_age":"1 Years(s) 0 Month(s) 22 Day(s)","duration":30,"appointment_time":"14:30:00","appointment_datetime":"2025-07-17 12:00:00","add_video_conferencing":0,"invoiced":0,"paid_amount":0,"position_in_queue":0,"reminded":1,"doctype":"Patient Appointment","vaccines":[],"__last_sync_on":"2025-07-17T03:49:33.540Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
